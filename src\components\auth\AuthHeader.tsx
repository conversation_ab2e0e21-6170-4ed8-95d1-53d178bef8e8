
import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Sparkles } from 'lucide-react';

const AuthHeader: React.FC = () => {
  return (
    <CardHeader className="text-center pb-3 pt-8">
      <div className="flex items-center justify-center mb-4">
        <Sparkles className="h-8 w-8 text-red-600" />
      </div>
      <CardTitle className="text-4xl font-bold text-red-600">Laufliste</CardTitle>
    </CardHeader>
  );
};

export default AuthHeader;
