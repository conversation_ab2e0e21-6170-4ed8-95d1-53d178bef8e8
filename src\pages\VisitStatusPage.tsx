
import React from 'react';
import { useParams } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import VisitStatus from '@/components/visit/VisitStatus';

const VisitStatusPage: React.FC = () => {
  const { visitId } = useParams<{ visitId: string }>();
  
  if (!visitId) {
    return (
      <MainLayout title="Besuchsstatus">
        <div className="p-4 text-center">
          Keine Besuchs-ID gefunden.
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout title="Besuchsstatus">
      <VisitStatus visitId={visitId} />
    </MainLayout>
  );
};

export default VisitStatusPage;
