
import { 
  Address, 
  House, 
  Visit, 
  Door, 
  ProductEntry,
  VisitStatus,
} from "@/types";

export interface DataContextType {
  addresses: Address[];
  houses: House[];
  visits: Visit[];
  doors: Door[];
  products: ProductEntry[];
  
  // Address operations
  addAddress: (address: Omit<Address, "id">) => Address;
  getAddressById: (id: string) => Address | undefined;
  
  // House operations
  addHouse: (house: Omit<House, "id" | "createdAt" | "createdBy">) => House;
  getHouseById: (id: string) => House | undefined;
  getHousesByAddress: (addressId: string) => House[];
  
  // Visit operations
  addVisit: (visit: Omit<Visit, "id" | "userId">) => Visit;
  getVisit: (id: string) => Visit | undefined;
  getVisitsByHouse: (houseId: string) => Visit[];
  getTodaysHouses: () => House[];
  getTodaysVisits: () => Visit[];
  updateVisitStatus: (visitId: string, status: VisitStatus) => void;
  
  // Door operations
  addDoor: (door: Omit<Door, "id">) => Door;
  getDoorsByVisit: (visitId: string) => Door[];
  updateDoorStatus: (doorId: string, status: VisitStatus) => void;
  
  // Product operations
  addProduct: (product: Omit<ProductEntry, "id" | "userId" | "timestamp">) => ProductEntry;
  getProductsByDoor: (doorId: string) => ProductEntry[];
}
