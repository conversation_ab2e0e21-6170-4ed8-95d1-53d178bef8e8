
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { User } from '@/types';
import { 
  Sidebar, 
  SidebarContent, 
  SidebarMenu, 
  SidebarMenuItem, 
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarFooter
} from '@/components/ui/sidebar';
import { 
  LayoutPanelLeft, 
  Home, 
  Map, 
  BarChart4, 
  User as UserIcon, 
  LogOut
} from 'lucide-react';
import { SidebarNavigation } from './SidebarNavigation';

interface MainSidebarProps {
  user: User | null;
  logout: () => Promise<void>;
}

export const MainSidebar: React.FC<MainSidebarProps> = ({ user, logout }) => {
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  return (
    <Sidebar>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <a href="/" className="flex items-center">
                    <Home className="mr-2 h-4 w-4" />
                    <span>Laufliste</span>
                  </a>
                </SidebarMenuButton>
              </SidebarMenuItem>
              
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <a href="/map" className="flex items-center">
                    <Map className="mr-2 h-4 w-4" />
                    <span>Kartenansicht</span>
                  </a>
                </SidebarMenuButton>
              </SidebarMenuItem>

              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <a href="/statistics" className="flex items-center">
                    <BarChart4 className="mr-2 h-4 w-4" />
                    <span>Statistiken</span>
                  </a>
                </SidebarMenuButton>
              </SidebarMenuItem>

              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <a href="/daily-view" className="flex items-center">
                    <LayoutPanelLeft className="mr-2 h-4 w-4" />
                    <span>Tagesübersicht</span>
                  </a>
                </SidebarMenuButton>
              </SidebarMenuItem>

              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <a href="/profile" className="flex items-center">
                    <UserIcon className="mr-2 h-4 w-4" />
                    <span>Profil</span>
                  </a>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        
        {/* Role-specific navigation */}
        {user && <SidebarNavigation user={user} />}
      </SidebarContent>
      
      {/* Logout button at the bottom of the sidebar */}
      {user && (
        <SidebarFooter>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton onClick={handleLogout} className="w-full">
                <LogOut className="mr-2 h-4 w-4" />
                <span>Logout</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
      )}
    </Sidebar>
  );
};
