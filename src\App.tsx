
import React from 'react';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import Index from './pages/Index';
import StatisticsPage from './pages/StatisticsPage';
import MapPage from './pages/MapPage';
import LoginPage from './pages/Login';
import { AuthProvider } from './context/auth';
import { DataProvider } from './context/data';
import ProtectedRoute from './components/ProtectedRoute';
import ProfilePage from './pages/ProfilePage';
import VisitStatusPage from './pages/VisitStatusPage';
import { SidebarProvider } from './components/ui/sidebar';
import DailyViewPage from './pages/DailyViewPage';
import SettingsPage from './pages/SettingsPage';
import UserManagementPage from './pages/UserManagementPage';
import TeamOverviewPage from './pages/TeamOverviewPage';
import BeraterStatisticsPage from './pages/BeraterStatisticsPage';
import TeamsOverviewPage from './pages/TeamsOverviewPage';
import TeamsStatisticsPage from './pages/TeamsStatisticsPage';
import AreaOverviewPage from './pages/AreaOverviewPage';
import ProductSelectionPage from './pages/ProductSelectionPage';
import MFHManagerPage from './pages/MFHManagerPage';

function App() {
  return (
    <AuthProvider>
      <DataProvider>
        <SidebarProvider>
          <Router>
            <Routes>
              <Route path="/login" element={<LoginPage />} />
              
              {/* Protected Routes */}
              <Route path="/" element={<ProtectedRoute><Index /></ProtectedRoute>} />
              <Route path="/statistics" element={<ProtectedRoute><StatisticsPage /></ProtectedRoute>} />
              <Route path="/map" element={<ProtectedRoute><MapPage /></ProtectedRoute>} />
              <Route path="/profile" element={<ProtectedRoute><ProfilePage /></ProtectedRoute>} />
              <Route path="/visit-status/:visitId" element={<ProtectedRoute><VisitStatusPage /></ProtectedRoute>} />
              <Route path="/products/:visitId" element={<ProtectedRoute><ProductSelectionPage /></ProtectedRoute>} />
              <Route path="/mfh/:houseId" element={<ProtectedRoute><MFHManagerPage /></ProtectedRoute>} />
              <Route path="/daily-view" element={<ProtectedRoute><DailyViewPage /></ProtectedRoute>} />
              <Route path="/settings" element={<ProtectedRoute><SettingsPage /></ProtectedRoute>} />
              
              {/* Admin Routes */}
              <Route path="/user-management" element={<ProtectedRoute><UserManagementPage /></ProtectedRoute>} />
              
              {/* Mentor Routes */}
              <Route path="/team-overview" element={<ProtectedRoute><TeamOverviewPage /></ProtectedRoute>} />
              <Route path="/berater-statistics" element={<ProtectedRoute><BeraterStatisticsPage /></ProtectedRoute>} />
              
              {/* Teamleiter Routes */}
              <Route path="/teams-overview" element={<ProtectedRoute><TeamsOverviewPage /></ProtectedRoute>} />
              <Route path="/teams-statistics" element={<ProtectedRoute><TeamsStatisticsPage /></ProtectedRoute>} />
              
              {/* Gebietsmanager Routes */}
              <Route path="/area-overview" element={<ProtectedRoute><AreaOverviewPage /></ProtectedRoute>} />
            </Routes>
          </Router>
        </SidebarProvider>
      </DataProvider>
    </AuthProvider>
  );
}

export default App;
