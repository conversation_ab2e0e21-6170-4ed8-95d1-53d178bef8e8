
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useData } from '@/context/data';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

interface BeraterDashboardProps {
  userId: string;
}

const BeraterDashboard: React.FC<BeraterDashboardProps> = ({ userId }) => {
  const { visits, doors, products } = useData();
  
  // Filtere Besuche für den aktuellen Berater
  const userVisits = visits.filter(v => v.userId === userId);
  
  // Berechne die Anzahl der angetroffenen Türen
  const doorsWithInteraction = doors.filter(d => 
    d.status === 'Angetroffen → Termin' || 
    d.status === 'Angetroffen → Kein Interesse' || 
    d.status === 'Angetroffen → Sale'
  );
  
  // Berechne die Anzahl der Verkäufe (Produkte) des Beraters
  const userProducts = products.filter(p => p.userId === userId);
  
  // Berechne die heutigen Besuche
  const today = new Date().toISOString().split('T')[0];
  const todayVisits = userVisits.filter(v => v.timestamp.startsWith(today));

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Berater Dashboard</h2>
      <p className="text-muted-foreground">
        Willkommen zurück! Hier ist Ihre aktuelle Übersicht.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Gesamtbesuche</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold">{userVisits.length}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Angetroffen</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold">{doorsWithInteraction.length}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Verkäufe</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold">{userProducts.length}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Heute</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold">{todayVisits.length}</p>
            <p className="text-xs text-muted-foreground">Besuche</p>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Aktuelle Periode</CardTitle>
          <CardDescription>
            {format(new Date(), "'Woche vom' d. MMMM yyyy", { locale: de })}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p>Tragen Sie weiterhin Ihre Besuche ein und pflegen Sie die Ergebnisse.</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default BeraterDashboard;
