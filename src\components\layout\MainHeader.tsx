
import React from 'react';
import { User } from '@/types';
import { SidebarTrigger } from '@/components/ui/sidebar';

interface MainHeaderProps {
  title?: string;
  user: User | null;
  isMobile: boolean;
}

export const MainHeader: React.FC<MainHeaderProps> = ({ title, user, isMobile }) => {
  return (
    <header className="flex items-center justify-between h-12 md:h-16 bg-white border-b border-gray-200 w-full">
      <div className="flex items-center px-2">
        <SidebarTrigger className="mr-2 md:mr-4" />
        <h1 className="text-lg md:text-2xl font-semibold truncate">{title || "Dashboard"}</h1>
      </div>
      
      {user && !isMobile && (
        <div className="hidden md:flex items-center space-x-4 px-2">
          <span className="text-gray-700">
            Eingeloggt als: {user.name} ({user.role})
          </span>
        </div>
      )}
    </header>
  );
};
