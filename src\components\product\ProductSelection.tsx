
import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Plus, MinusCircle } from 'lucide-react';
import { useData } from '@/context/data';
import { productOptions, ProductCategory, Door } from '@/types';
import { toast } from 'sonner';

const ProductSelection: React.FC = () => {
  const { visitId } = useParams<{ visitId: string }>();
  const navigate = useNavigate();
  const { 
    addProduct, 
    visits, 
    getDoorsByVisit,
    getHouseById,
    getAddressById
  } = useData();

  const [selectedDoor, setSelectedDoor] = useState<string | null>(null);
  const [products, setProducts] = useState<{category: ProductCategory; type: string; quantity: number}[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const visit = visits.find(v => v.id === visitId);
  if (!visit) {
    navigate('/');
    return null;
  }

  const doors = getDoorsByVisit(visitId!);
  const salesDoors = doors.filter(door => door.status === 'Angetroffen → Sale');
  
  useEffect(() => {
    if (salesDoors.length > 0 && !selectedDoor) {
      setSelectedDoor(salesDoors[0].id);
    }
  }, [salesDoors]);

  const house = getHouseById(visit.houseId);
  if (!house) {
    navigate('/');
    return null;
  }

  const address = getAddressById(house.addressId);
  if (!address) {
    navigate('/');
    return null;
  }

  const addProductItem = (category: ProductCategory) => {
    const defaultOption = productOptions.find(option => option.category === category);
    if (defaultOption) {
      setProducts([...products, { category, type: defaultOption.type, quantity: 1 }]);
    }
  };

  const removeProductItem = (index: number) => {
    const newProducts = [...products];
    newProducts.splice(index, 1);
    setProducts(newProducts);
  };

  const updateProductType = (index: number, type: string) => {
    const newProducts = [...products];
    newProducts[index].type = type;
    setProducts(newProducts);
  };

  const updateProductQuantity = (index: number, quantity: number) => {
    if (quantity < 1) return;
    
    const newProducts = [...products];
    newProducts[index].quantity = quantity;
    setProducts(newProducts);
  };

  const handleSubmit = async () => {
    if (!selectedDoor) {
      toast.error('Bitte wählen Sie eine Tür aus');
      return;
    }

    setIsSubmitting(true);

    try {
      // Save all products
      for (const product of products) {
        addProduct({
          doorId: selectedDoor,
          category: product.category,
          type: product.type,
          quantity: product.quantity,
        });
      }

      toast.success('Produkte erfolgreich gespeichert');
      navigate('/daily-view');
    } catch (error) {
      console.error(error);
      toast.error('Fehler beim Speichern der Produkte');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Produkte auswählen</CardTitle>
      </CardHeader>
      <CardContent>
        {salesDoors.length > 1 && (
          <div className="mb-6">
            <Label htmlFor="door-selection">Tür auswählen</Label>
            <Select 
              value={selectedDoor || ''} 
              onValueChange={setSelectedDoor}
            >
              <SelectTrigger id="door-selection">
                <SelectValue placeholder="Tür auswählen" />
              </SelectTrigger>
              <SelectContent>
                {salesDoors.map(door => (
                  <SelectItem key={door.id} value={door.id}>
                    {door.name} {door.floor ? `(${door.floor})` : ''}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        <Tabs defaultValue="kip">
          <TabsList className="mb-4 w-full grid grid-cols-3">
            <TabsTrigger value="kip">KIP</TabsTrigger>
            <TabsTrigger value="tv">TV</TabsTrigger>
            <TabsTrigger value="mobile">Mobile</TabsTrigger>
          </TabsList>
          
          {/* KIP Tab */}
          <TabsContent value="kip" className="space-y-4">
            {products.filter(p => p.category === 'KIP').map((product, index) => (
              <div key={index} className="flex items-end gap-2 p-3 border rounded-md">
                <div className="flex-1">
                  <Label htmlFor={`kip-quantity-${index}`}>Anzahl</Label>
                  <Input 
                    id={`kip-quantity-${index}`}
                    type="number" 
                    min={1}
                    value={product.quantity} 
                    onChange={(e) => updateProductQuantity(
                      products.findIndex(p => p === product), 
                      parseInt(e.target.value)
                    )} 
                  />
                </div>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="mb-0.5"
                  onClick={() => removeProductItem(products.findIndex(p => p === product))}
                >
                  <MinusCircle size={18} className="text-destructive" />
                </Button>
              </div>
            ))}
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => addProductItem('KIP')}
            >
              <Plus size={16} className="mr-2" />
              KIP hinzufügen
            </Button>
          </TabsContent>
          
          {/* TV Tab */}
          <TabsContent value="tv" className="space-y-4">
            {products.filter(p => p.category === 'TV').map((product, index) => (
              <div key={index} className="flex flex-col gap-2 p-3 border rounded-md">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">TV Option {index + 1}</h4>
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={() => removeProductItem(products.findIndex(p => p === product))}
                  >
                    <MinusCircle size={18} className="text-destructive" />
                  </Button>
                </div>
                
                <div>
                  <Label htmlFor={`tv-type-${index}`}>Typ</Label>
                  <Select 
                    value={product.type} 
                    onValueChange={(value) => updateProductType(
                      products.findIndex(p => p === product),
                      value
                    )}
                  >
                    <SelectTrigger id={`tv-type-${index}`}>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {productOptions
                        .filter(option => option.category === 'TV')
                        .map(option => (
                          <SelectItem key={option.type} value={option.type}>
                            {option.label}
                          </SelectItem>
                        ))
                      }
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor={`tv-quantity-${index}`}>Anzahl</Label>
                  <Input 
                    id={`tv-quantity-${index}`}
                    type="number" 
                    min={1}
                    value={product.quantity} 
                    onChange={(e) => updateProductQuantity(
                      products.findIndex(p => p === product), 
                      parseInt(e.target.value)
                    )} 
                  />
                </div>
              </div>
            ))}
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => addProductItem('TV')}
            >
              <Plus size={16} className="mr-2" />
              TV Option hinzufügen
            </Button>
          </TabsContent>
          
          {/* Mobile Tab */}
          <TabsContent value="mobile" className="space-y-4">
            {products.filter(p => p.category === 'Mobile').map((product, index) => (
              <div key={index} className="flex flex-col gap-2 p-3 border rounded-md">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">Mobile Option {index + 1}</h4>
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={() => removeProductItem(products.findIndex(p => p === product))}
                  >
                    <MinusCircle size={18} className="text-destructive" />
                  </Button>
                </div>
                
                <div>
                  <Label htmlFor={`mobile-type-${index}`}>Typ</Label>
                  <Select 
                    value={product.type} 
                    onValueChange={(value) => updateProductType(
                      products.findIndex(p => p === product),
                      value
                    )}
                  >
                    <SelectTrigger id={`mobile-type-${index}`}>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {productOptions
                        .filter(option => option.category === 'Mobile')
                        .map(option => (
                          <SelectItem key={option.type} value={option.type}>
                            {option.label}
                          </SelectItem>
                        ))
                      }
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor={`mobile-quantity-${index}`}>Anzahl</Label>
                  <Input 
                    id={`mobile-quantity-${index}`}
                    type="number" 
                    min={1}
                    value={product.quantity} 
                    onChange={(e) => updateProductQuantity(
                      products.findIndex(p => p === product), 
                      parseInt(e.target.value)
                    )} 
                  />
                </div>
              </div>
            ))}
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => addProductItem('Mobile')}
            >
              <Plus size={16} className="mr-2" />
              Mobile Option hinzufügen
            </Button>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter>
        <Button 
          onClick={handleSubmit}
          className="w-full" 
          disabled={!selectedDoor || isSubmitting}
        >
          {isSubmitting ? 'Speichern...' : 'Speichern und abschließen'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ProductSelection;
