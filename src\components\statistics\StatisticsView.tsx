
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { useData } from '@/context/data';
import { VisitStatus, ProductCategory } from '@/types';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { useAuth } from '@/context/auth';
import { useIsMobile } from '@/hooks/use-mobile';

const StatisticsView: React.FC = () => {
  const { houses, visits, doors, products } = useData();
  const { user } = useAuth();
  const [period, setPeriod] = useState<'day' | 'week' | 'month'>('day');
  const isMobile = useIsMobile();

  // Calculate statistics
  const getTotalDoors = () => {
    const efhCount = visits.filter(v => {
      const house = houses.find(h => h.id === v.houseId);
      return house?.type === 'EFH';
    }).length;
    
    const mfhDoorCount = doors.length;
    
    return efhCount + mfhDoorCount;
  };

  const getStatusCount = (status: VisitStatus) => {
    return doors.filter(d => d.status === status).length;
  };

  const getProductCount = (category: ProductCategory) => {
    return products.filter(p => p.category === category).length;
  };

  // Prepare chart data
  const statusData = [
    { name: 'N/A', count: getStatusCount('N/A') },
    { name: 'Termin', count: getStatusCount('Angetroffen → Termin') },
    { name: 'Kein Interesse', count: getStatusCount('Angetroffen → Kein Interesse') },
    { name: 'Sale', count: getStatusCount('Angetroffen → Sale') },
  ];

  const productData = [
    { name: 'KIP', count: getProductCount('KIP') },
    { name: 'TV', count: getProductCount('TV') },
    { name: 'Mobile', count: getProductCount('Mobile') },
  ];

  // Create Statistics Content component to avoid repetition
  const StatisticsContent = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Zusammenfassung</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 bg-gray-50 rounded-lg text-center">
              <p className="text-muted-foreground text-sm">Gesamttüren</p>
              <p className="text-3xl font-bold">{getTotalDoors()}</p>
            </div>
            <div className="p-4 bg-green-50 rounded-lg text-center">
              <p className="text-muted-foreground text-sm">Sales</p>
              <p className="text-3xl font-bold text-green-600">{getStatusCount('Angetroffen → Sale')}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Besuchsstatus</CardTitle>
        </CardHeader>
        <CardContent>
          <div style={{ height: isMobile ? '200px' : '300px' }} className="w-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={statusData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="count" name="Anzahl" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Produktverkäufe</CardTitle>
        </CardHeader>
        <CardContent>
          <div style={{ height: isMobile ? '200px' : '300px' }} className="w-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={productData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="count" name="Anzahl" fill="#10b981" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className={`${isMobile ? 'px-2 py-4' : 'p-4'} space-y-4 w-full`}>
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Statistiken</h2>
        <Button variant="outline" size="icon">
          <Download size={16} />
        </Button>
      </div>

      <Tabs defaultValue="day" onValueChange={(v) => setPeriod(v as any)}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="day">Tag</TabsTrigger>
          <TabsTrigger value="week">Woche</TabsTrigger>
          <TabsTrigger value="month">Monat</TabsTrigger>
        </TabsList>
        
        <TabsContent value="day" className="mt-4">
          <StatisticsContent />
        </TabsContent>

        <TabsContent value="week" className="mt-4">
          <StatisticsContent />
        </TabsContent>

        <TabsContent value="month" className="mt-4">
          <StatisticsContent />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default StatisticsView;
