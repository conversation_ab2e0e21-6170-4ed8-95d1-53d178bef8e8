
import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import AddressForm from '@/components/address/AddressForm';
import { Home, Building } from 'lucide-react';

const Index = () => {
  return (
    <MainLayout title="Laufliste">
      <div className="space-y-6 max-w-5xl mx-auto">
        <Tabs defaultValue="efh" className="w-full">
          <TabsList className="grid grid-cols-2 mb-6">
            <TabsTrigger value="efh" className="text-base">
              <Home className="mr-2 h-4 w-4" />
              EFH
            </TabsTrigger>
            <TabsTrigger value="mfh" className="text-base">
              <Building className="mr-2 h-4 w-4" />
              MFH
            </TabsTrigger>
          </TabsList>

          <TabsContent value="efh" className="p-1">
            <AddressForm houseType="EFH" />
          </TabsContent>

          <TabsContent value="mfh" className="p-1">
            <AddressForm houseType="MFH" />
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default Index;
