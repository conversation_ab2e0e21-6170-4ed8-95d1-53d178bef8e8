
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { useData } from '@/context/data';

interface VisitStatusProps {
  visitId: string;
}

const VisitStatus: React.FC<VisitStatusProps> = ({ visitId }) => {
  const { getVisit } = useData();
  const visit = getVisit(visitId);

  if (!visit) {
    return <Badge variant="outline">Nicht verfügbar</Badge>;
  }

  let statusText: string;
  let statusClassName: string;

  switch (visit.status) {
    case 'Angetroffen → Termin':
      statusText = 'Termin';
      statusClassName = 'bg-blue-100 text-blue-800 border-blue-200';
      break;
    case 'Angetroffen → Kein Interesse':
      statusText = 'Kein Interesse';
      statusClassName = 'bg-gray-100 text-gray-800 border-gray-200';
      break;
    case 'Angetroffen → Sale':
      statusText = 'Verkauf';
      statusClassName = 'bg-green-100 text-green-800 border-green-200';
      break;
    default:
      statusText = 'N/A';
      statusClassName = 'bg-yellow-100 text-yellow-800 border-yellow-200';
      break;
  }

  return (
    <Badge className={statusClassName} variant="outline">
      {statusText}
    </Badge>
  );
};

export default VisitStatus;
