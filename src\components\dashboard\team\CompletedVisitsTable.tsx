
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { CompletedVisitData } from './types';
import { useIsMobile } from '@/hooks/use-mobile';

interface CompletedVisitsTableProps {
  completedVisits: CompletedVisitData[];
  teamName?: string;
}

const CompletedVisitsTable: React.FC<CompletedVisitsTableProps> = ({ 
  completedVisits, 
  teamName = "Teams" 
}) => {
  const isMobile = useIsMobile();
  
  return (
    <Card className="w-full border-[1px] shadow-none">
      <CardHeader className="py-1 px-2">
        <CardTitle className="text-sm">Abgeschlossene Adressen</CardTitle>
        <CardDescription className="text-xs">
          <PERSON><PERSON><PERSON> von {teamName} ({completedVisits.length} Einträge)
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <div className="w-full">
          <Table>
            <TableHeader>
              <TableRow className="border-b border-gray-100">
                <TableHead className="text-xs font-normal py-1 px-2 whitespace-nowrap">Datum</TableHead>
                <TableHead className="text-xs font-normal py-1 px-2 whitespace-nowrap">Adresse</TableHead>
                {!isMobile && <TableHead className="text-xs font-normal py-1 px-2">Berater</TableHead>}
                {!isMobile && <TableHead className="text-xs font-normal py-1 px-2">Mentor</TableHead>}
                <TableHead className="text-xs font-normal py-1 px-2 whitespace-nowrap">Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {completedVisits.map((item, index) => (
                <TableRow key={index} className="border-b border-gray-100">
                  <TableCell className="text-xs py-1.5 px-2 whitespace-nowrap">{item.visitDate}</TableCell>
                  <TableCell className="text-xs py-1.5 px-2 max-w-[120px] truncate">{item.address}</TableCell>
                  {!isMobile && <TableCell className="text-xs py-1.5 px-2">{item.beraterName}</TableCell>}
                  {!isMobile && <TableCell className="text-xs py-1.5 px-2">{item.mentorName}</TableCell>}
                  <TableCell className="text-xs py-1.5 px-2 whitespace-nowrap">
                    <Badge className="bg-green-500 text-xs font-normal px-1 py-0.5 h-auto">Abgeschlossen</Badge>
                  </TableCell>
                </TableRow>
              ))}
              {completedVisits.length === 0 && (
                <TableRow>
                  <TableCell colSpan={isMobile ? 3 : 5} className="text-center text-xs py-4 px-2">
                    Keine abgeschlossenen Adressen
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default CompletedVisitsTable;
