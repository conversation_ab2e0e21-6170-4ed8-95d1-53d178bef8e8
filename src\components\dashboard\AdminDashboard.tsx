import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useAuth } from '@/context/auth';
import { useData } from '@/context/data';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { UserRole } from '@/types';
import { Settings, Users, Activity, Database } from 'lucide-react';

const AdminDashboard: React.FC = () => {
  const { users } = useAuth();
  const { addresses, houses, visits, doors, products } = useData();
  
  const userCountByRole = {
    admin: users.filter(u => u.role === 'admin').length,
    gebietsmanager: users.filter(u => u.role === 'gebietsmanager').length,
    teamleiter: users.filter(u => u.role === 'teamleiter').length,
    mentor: users.filter(u => u.role === 'mentor').length,
    berater: users.filter(u => u.role === 'berater').length,
  };

  // Anzahl der Verkäufe pro Produktkategorie
  const productsByCategory = {
    kip: products.filter(p => p.category === 'KIP').length,
    tv: products.filter(p => p.category === 'TV').length,
    mobile: products.filter(p => p.category === 'Mobile').length,
  };

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Admin Dashboard</h2>
      <p className="text-muted-foreground">
        Systemverwaltung und Übersichten
      </p>
      
      <Tabs defaultValue="users" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="users" className="flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span>Benutzer</span>
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center space-x-2">
            <Activity className="h-4 w-4" />
            <span>Aktivitäten</span>
          </TabsTrigger>
          <TabsTrigger value="data" className="flex items-center space-x-2">
            <Database className="h-4 w-4" />
            <span>Daten</span>
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span>System</span>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="users" className="space-y-4 mt-4">
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {Object.entries(userCountByRole).map(([role, count]) => (
              <Card key={role}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground capitalize">
                    {role}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-3xl font-bold">{count}</p>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Benutzerübersicht</CardTitle>
              <CardDescription>
                Verwaltung aller Benutzer im System
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>E-Mail</TableHead>
                    <TableHead>Rolle</TableHead>
                    <TableHead>Team</TableHead>
                    <TableHead>Aktionen</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map(user => (
                    <TableRow key={user.id}>
                      <TableCell>{user.name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell className="capitalize">{user.role}</TableCell>
                      <TableCell>{user.teamId || '-'}</TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm">Bearbeiten</Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="activity" className="space-y-4 mt-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Besuche</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold">{visits.length}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Türen</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold">{doors.length}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Verkäufe</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold">{products.length}</p>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Produktverteilung</CardTitle>
              <CardDescription>
                Übersicht aller verkauften Produkte nach Kategorie
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">KIP</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-3xl font-bold">{productsByCategory.kip}</p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">TV</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-3xl font-bold">{productsByCategory.tv}</p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">Mobile</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-3xl font-bold">{productsByCategory.mobile}</p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="data" className="space-y-4 mt-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Adressen</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold">{addresses.length}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Häuser</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold">{houses.length}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">EFH</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold">{houses.filter(h => h.type === 'EFH').length}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">MFH</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold">{houses.filter(h => h.type === 'MFH').length}</p>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Datenübersicht</CardTitle>
              <CardDescription>
                {format(new Date(), "'Stand:' d. MMMM yyyy", { locale: de })}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>In der Datenbank befinden sich:</p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>{addresses.length} Adressen</li>
                <li>{houses.length} Häuser ({houses.filter(h => h.type === 'EFH').length} EFH, {houses.filter(h => h.type === 'MFH').length} MFH)</li>
                <li>{visits.length} Besuche</li>
                <li>{doors.length} erfasste Türen</li>
                <li>{products.length} verkaufte Produkte</li>
              </ul>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="system" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Systemkonfiguration</CardTitle>
              <CardDescription>
                Verwaltung der Systemeinstellungen
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>Hier können Systemeinstellungen verwaltet werden.</p>
              <div className="mt-4 space-y-2">
                <Button variant="outline">Daten exportieren</Button>
                <Button variant="outline" className="ml-2">System zurücksetzen</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminDashboard;
