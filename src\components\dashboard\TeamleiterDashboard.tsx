
import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useTeamData } from './team/useTeamData';
import TeamStatsCards from './team/TeamStatsCards';
import TeamsOverviewTable from './team/TeamsOverviewTable';
import CompletedVisitsTable from './team/CompletedVisitsTable';
import { TeamleiterDashboardProps } from './team/types';
import { useIsMobile } from '@/hooks/use-mobile';

const TeamleiterDashboard: React.FC<TeamleiterDashboardProps> = ({ teamId }) => {
  const [activeTab, setActiveTab] = useState<string>("overview");
  const { 
    loading, 
    teamStats, 
    selectedTeam, 
    completedVisits 
  } = useTeamData(teamId);
  const isMobile = useIsMobile();

  if (loading) {
    return <div className="flex justify-center items-center w-full p-4">Lade Teams...</div>;
  }

  return (
    <div className="h-full flex flex-col w-full">
      <div className="px-4 py-2">
        <h2 className="text-lg font-semibold">Team Dashboard</h2>
        {selectedTeam && (
          <p className="text-xs text-muted-foreground">
            {selectedTeam.name} | {teamStats.find(t => t.id === teamId)?.memberCount || 0} Mitglieder
          </p>
        )}
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col w-full">
        <TabsList className="w-full mx-4 mb-2">
          <TabsTrigger value="overview" className="flex-1 text-xs">Teams Übersicht</TabsTrigger>
          <TabsTrigger value="completed" className="flex-1 text-xs">Abgeschlossene Adressen</TabsTrigger>
        </TabsList>
        
        <div className="flex-1 overflow-hidden w-full px-4">
          <TabsContent value="overview" className="m-0 h-full space-y-2 w-full">
            <TeamStatsCards teamStats={teamStats} />
            <TeamsOverviewTable teamStats={teamStats} />
          </TabsContent>
            
          <TabsContent value="completed" className="m-0 h-full w-full">
            <CompletedVisitsTable 
              completedVisits={completedVisits}
              teamName={selectedTeam?.name} 
            />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};

export default TeamleiterDashboard;
