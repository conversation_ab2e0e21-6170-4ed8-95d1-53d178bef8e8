
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/context/auth';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { Switch } from '@/components/ui/switch';

interface LoginFormProps {
  email: string;
  setEmail: (email: string) => void;
  password: string;
  setPassword: (password: string) => void;
}

const LoginForm: React.FC<LoginFormProps> = ({ 
  email, 
  setEmail, 
  password, 
  setPassword 
}) => {
  const navigate = useNavigate();
  const { login, isLoading } = useAuth();
  const [showAccountSwitcher, setShowAccountSwitcher] = useState(false);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const success = await login(email, password);
      if (success) {
        navigate('/');
        // Toast is shown by the login function on success
      }
    } catch (error) {
      // Toast is shown by the login function on error
      console.error('Login error:', error);
    }
  };

  const handleQuickLogin = async (role: string) => {
    let accountEmail = '';
    const accountPassword = 'test123';

    switch(role) {
      case 'berater':
        accountEmail = '<EMAIL>';
        break;
      case 'mentor':
        accountEmail = '<EMAIL>';
        break;
      case 'teamleiter':
        accountEmail = '<EMAIL>';
        break;
      case 'manager':
        accountEmail = '<EMAIL>';
        break;
      case 'admin':
        accountEmail = '<EMAIL>';
        break;
      default:
        accountEmail = '<EMAIL>';
    }

    setEmail(accountEmail);
    setPassword(accountPassword);

    try {
      const success = await login(accountEmail, accountPassword);
      if (success) {
        navigate('/');
      }
    } catch (error) {
      console.error('Quick login error:', error);
    }
  };

  return (
    <form onSubmit={handleLogin} className="space-y-4">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm text-gray-500">Test-Account Switcher</span>
        <Switch 
          checked={showAccountSwitcher}
          onCheckedChange={setShowAccountSwitcher}
        />
      </div>

      {showAccountSwitcher && (
        <div className="p-3 mb-4 border rounded-md bg-gray-50">
          <h3 className="mb-2 text-sm font-medium">Schnellauswahl:</h3>
          <div className="grid grid-cols-2 gap-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => handleQuickLogin('berater')}
              className="h-auto py-1 text-xs"
            >
              Login als Berater
            </Button>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => handleQuickLogin('mentor')}
              className="h-auto py-1 text-xs"
            >
              Login als Mentor
            </Button>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => handleQuickLogin('teamleiter')}
              className="h-auto py-1 text-xs"
            >
              Login als Teamleiter
            </Button>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => handleQuickLogin('manager')}
              className="h-auto py-1 text-xs"
            >
              Login als Manager
            </Button>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => handleQuickLogin('admin')}
              className="col-span-2 h-auto py-1 text-xs"
            >
              Login als Admin
            </Button>
          </div>
        </div>
      )}

      <div className="space-y-2">
        <Label htmlFor="email">E-Mail oder Name</Label>
        <Input
          id="email"
          type="text"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="bg-white"
          placeholder="<EMAIL> oder Name"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="password">Passwort</Label>
        <Input
          id="password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          className="bg-white"
          placeholder="••••••••"
        />
      </div>
      <Button
        type="submit"
        className="w-full bg-red-600 hover:bg-red-700"
        disabled={isLoading || !email || !password}
      >
        {isLoading ? 'Anmeldung...' : 'Anmelden'}
      </Button>
    </form>
  );
};

export default LoginForm;
