
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from '@/components/ui/card';
import { useData } from '@/context/data';
import { useAuth } from '@/context/auth';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

const GebietsmanagerDashboard: React.FC = () => {
  const { visits, houses, products } = useData();
  const { users } = useAuth();
  
  // Finde alle Teams und deren IDs
  const teams = [...new Set(users
    .filter(user => user.teamId)
    .map(user => user.teamId))];
  
  // Statistiken pro Team
  const teamStats = teams.map(teamId => {
    const teamMembers = users.filter(user => user.teamId === teamId);
    const teamLeader = teamMembers.find(user => user.role === 'teamleiter')?.name || 'Ni<PERSON> zu<PERSON><PERSON>n';
    const beratersCount = teamMembers.filter(user => user.role === 'berater').length;
    const mentorsCount = teamMembers.filter(user => user.role === 'mentor').length;
    
    const teamVisits = visits.filter(v => {
      const visitUser = teamMembers.find(u => u.id === v.userId);
      return visitUser !== undefined;
    });
    
    const teamProducts = products.filter(p => {
      const productUser = teamMembers.find(u => u.id === p.userId);
      return productUser !== undefined;
    });
    
    return {
      teamId,
      teamLeader,
      memberCount: teamMembers.length,
      beratersCount,
      mentorsCount,
      visitsCount: teamVisits.length,
      productsCount: teamProducts.length
    };
  });

  // Gesamtzahlen
  const totalUsers = users.length;
  const totalVisits = visits.length;
  const totalHouses = houses.length;
  const totalProducts = products.length;

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Gebietsmanager Dashboard</h2>
      <p className="text-muted-foreground">
        Gesamtübersicht über alle Teams und Aktivitäten
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Benutzer</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold">{totalUsers}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Teams</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold">{teams.length}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Häuser</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold">{totalHouses}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Verkäufe</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold">{totalProducts}</p>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Team-Übersicht</CardTitle>
          <CardDescription>
            {format(new Date(), "'Stand:' d. MMMM yyyy", { locale: de })}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Team</TableHead>
                <TableHead>Teamleiter</TableHead>
                <TableHead>Mitglieder</TableHead>
                <TableHead>Besuche</TableHead>
                <TableHead>Verkäufe</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {teamStats.map((team) => (
                <TableRow key={team.teamId?.toString()}>
                  <TableCell>{team.teamId}</TableCell>
                  <TableCell>{team.teamLeader}</TableCell>
                  <TableCell>{team.memberCount} ({team.beratersCount} Berater, {team.mentorsCount} Mentoren)</TableCell>
                  <TableCell>{team.visitsCount}</TableCell>
                  <TableCell>{team.productsCount}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default GebietsmanagerDashboard;
