
import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/context/auth';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';

const SettingsPage: React.FC = () => {
  const { user, users, updateUser } = useAuth();
  const isAdmin = user?.role === 'admin';
  const isTeamleiter = user?.role === 'teamleiter';
  const isGebietsmanager = user?.role === 'gebietsmanager';
  
  if (!isAdmin && !isTeamleiter && !isGebietsmanager) {
    return (
      <MainLayout title="Einstellungen">
        <div className="p-4 text-center">
          <PERSON>e haben keine Berechtigung, diese Seite anzuzeigen.
        </div>
      </MainLayout>
    );
  }
  
  // Filtere Benutzer basierend auf Rolle
  const filteredUsers = users.filter(u => {
    if (isAdmin) return true; // Admin sieht alle
    if (isGebietsmanager) return u.role !== 'admin'; // Gebietsmanager sieht nicht Admins
    if (isTeamleiter) return u.teamId === user?.teamId; // Teamleiter sieht nur sein Team
    return false;
  });
  
  return (
    <MainLayout title="Einstellungen">
      <div className="space-y-6">
        <Tabs defaultValue="team" className="w-full">
          <TabsList className="mb-4">
            {(isAdmin || isGebietsmanager) && (
              <TabsTrigger value="general">Allgemein</TabsTrigger>
            )}
            <TabsTrigger value="team">Team</TabsTrigger>
            {isAdmin && (
              <TabsTrigger value="system">System</TabsTrigger>
            )}
          </TabsList>
          
          {(isAdmin || isGebietsmanager) && (
            <TabsContent value="general" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Allgemeine Einstellungen</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Allgemeine Einstellungen für die App verwalten.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          )}
          
          <TabsContent value="team" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Team-Einstellungen</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>E-Mail</TableHead>
                      <TableHead>Rolle</TableHead>
                      {(isAdmin || isGebietsmanager) && <TableHead>Team</TableHead>}
                      <TableHead>Aktionen</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.map(u => (
                      <TableRow key={u.id}>
                        <TableCell>{u.name}</TableCell>
                        <TableCell>{u.email}</TableCell>
                        <TableCell className="capitalize">{u.role}</TableCell>
                        {(isAdmin || isGebietsmanager) && <TableCell>{u.teamId || '-'}</TableCell>}
                        <TableCell>
                          <Button variant="outline" size="sm">Bearbeiten</Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
          
          {isAdmin && (
            <TabsContent value="system" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>System-Einstellungen</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    System-Einstellungen und Konfiguration.
                  </p>
                  <div className="mt-4 space-x-2">
                    <Button variant="outline">Daten exportieren</Button>
                    <Button variant="outline">Daten importieren</Button>
                    <Button variant="destructive">Daten zurücksetzen</Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          )}
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default SettingsPage;
