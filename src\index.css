
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 0 72% 51%;  /* Red color */
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 0 72% 51%;  /* Red color for focus rings */

    --radius: 0.75rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 0 72% 51%;  /* Red color */
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 0 72% 51%;  /* Red color */
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 0 72% 51%;  /* Red color */
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 0 72% 51%;  /* Red color */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 0 72% 51%;  /* Red color */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
  
  /* Mobile optimizations */
  html, body {
    @apply touch-manipulation overscroll-none;
    scrollbar-width: none; /* Firefox */
  }
  
  html::-webkit-scrollbar, body::-webkit-scrollbar {
    display: none; /* Chrome, Safari, newer versions of Opera */
  }
}

/* Improved scrollbar hiding for all elements */
.scrollbar-none {
  -ms-overflow-style: none; /* Internet Explorer and Edge */
  scrollbar-width: none; /* Firefox */
  scroll-behavior: smooth;
}

.scrollbar-none::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none; /* Safari and Chrome */
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Animation utility classes */
.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.4s ease-out;
}

.animate-pulse-slow {
  animation: pulse 3s infinite;
}

.hover-scale {
  transition: transform 0.2s ease-out;
}

.hover-scale:hover {
  transform: scale(1.03);
}

.fade-in-section {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in-section.is-visible {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive helpers */
.responsive-container {
  @apply w-full px-3 md:px-6 py-3 md:py-6;
}

.responsive-card {
  @apply rounded-lg shadow-sm p-3 md:p-5;
}

.responsive-text {
  @apply text-sm md:text-base;
}

.responsive-heading {
  @apply text-lg md:text-xl lg:text-2xl font-bold;
}

.responsive-subheading {
  @apply text-base md:text-lg font-semibold;
}

/* Status colors for visits */
.status-na {
  @apply bg-gray-200 text-gray-700 border-gray-300;
}

.status-termin {
  @apply bg-yellow-100 text-yellow-700 border-yellow-300;
}

.status-kein-interesse {
  @apply bg-red-100 text-red-700 border-red-300;
}

.status-sale {
  @apply bg-green-100 text-green-700 border-green-300;
}

/* Map marker styles */
.map-marker {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.map-marker-completed {
  background-color: #22c55e;
}

.map-marker-pending {
  background-color: #ef4444;
}

/* Swipe transitions */
.swipe-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.swipe-page {
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
  transition: transform 0.3s ease;
}

.swipe-left {
  transform: translateX(-100%);
}

.swipe-right {
  transform: translateX(100%);
}

.swipe-current {
  transform: translateX(0);
}

/* Mobile table responsiveness */
.table-responsive {
  @apply w-full overflow-x-auto -mx-4 px-4;
}

.table-responsive table {
  @apply min-w-full;
}

/* Mobile-friendly form controls */
@media (max-width: 768px) {
  input, select, textarea, button {
    @apply text-base py-2.5 px-3;
  }
}
