
import React, { useEffect, useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/context/auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useData } from '@/context/data';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { ScrollArea } from '@/components/ui/scroll-area';

const TeamsStatisticsPage: React.FC = () => {
  const { user, users } = useAuth();
  const { visits, products } = useData();
  const [activeTeam, setActiveTeam] = useState<string | null>(null);

  // Define team data
  const teams = [
    { id: 'team-ulm', name: 'Team Ulm' },
    { id: 'team-stuttgart', name: 'Team Stuttgart' },
    { id: 'team-ludwigsburg', name: 'Team Ludwigsburg' }
  ];
  
  // Set initial active team
  useEffect(() => {
    if (user?.teamId && !activeTeam) {
      setActiveTeam(user.teamId);
    } else if (!activeTeam && teams.length > 0) {
      setActiveTeam(teams[0].id);
    }
  }, [user, activeTeam, teams]);

  if (!user || user.role !== 'teamleiter') {
    return (
      <MainLayout title="Teams Statistiken">
        <div className="p-4 text-center">
          Sie haben keine Berechtigung, diese Seite anzuzeigen.
        </div>
      </MainLayout>
    );
  }

  // Get selected team data
  const selectedTeam = teams.find(t => t.id === activeTeam) || teams[0];

  // Mock data for roles by team
  const getRoleData = (teamId: string) => {
    const roleMultipliers: Record<string, number> = {
      'team-ulm': 1,
      'team-stuttgart': 1.5,
      'team-ludwigsburg': 0.8
    };
    
    const multiplier = roleMultipliers[teamId] || 1;
    
    return [
      { name: 'Berater', value: Math.round(8 * multiplier) },
      { name: 'Mentoren', value: Math.round(4 * multiplier) },
      { name: 'Teamleiter', value: 1 }
    ];
  };

  const visitsByRole = getRoleData(activeTeam || '');
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28'];

  // Product statistics by category
  const getProductData = (teamId: string) => {
    const productMultipliers: Record<string, number> = {
      'team-ulm': 1.2,
      'team-stuttgart': 0.9,
      'team-ludwigsburg': 1.5
    };
    
    const multiplier = productMultipliers[teamId] || 1;
    
    return [
      { name: 'KIP', value: Math.round(15 * multiplier) },
      { name: 'TV', value: Math.round(10 * multiplier) },
      { name: 'Mobile', value: Math.round(20 * multiplier) }
    ];
  };
  
  const productsByCategory = getProductData(activeTeam || '');

  return (
    <MainLayout title="Teams Statistiken">
      <div className="h-full w-full max-w-[100vw] max-h-[calc(100vh-80px)]" style={{ overflow: 'hidden' }}>
        <ScrollArea className="h-full w-full scrollbar-none">
          <div className="h-full flex flex-col">
            <div className="space-y-4 p-4">
              <h2 className="text-xl font-bold">Statistiken für {selectedTeam.name}</h2>
              
              <div className="flex flex-wrap gap-2 mb-4">
                {teams.map(team => (
                  <button
                    key={team.id}
                    className={`px-3 py-1.5 text-sm rounded-full ${
                      activeTeam === team.id
                        ? 'bg-primary text-white'
                        : 'bg-secondary text-secondary-foreground'
                    }`}
                    onClick={() => setActiveTeam(team.id)}
                  >
                    {team.name}
                  </button>
                ))}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="h-[350px]">
                  <CardHeader>
                    <CardTitle>Team nach Rolle</CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <div className="h-[250px] w-full">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={visitsByRole}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {visitsByRole.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>

                <Card className="h-[350px]">
                  <CardHeader>
                    <CardTitle>Verkäufe nach Kategorie</CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <div className="h-[250px] w-full">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={productsByCategory}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="value" name="Anzahl" fill="#8884d8" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </ScrollArea>
      </div>
    </MainLayout>
  );
};

export default TeamsStatisticsPage;
