
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import LoginForm from '@/components/auth/LoginForm';
import RegisterForm from '@/components/auth/RegisterForm';
import AuthHeader from '@/components/auth/AuthHeader';

const Login = () => {
  const [activeTab, setActiveTab] = useState<"login" | "register">("login");
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-red-50 to-white">
      <Card className="w-[360px] max-w-[90%] mx-auto border shadow-lg rounded-lg overflow-hidden">
        <AuthHeader />
        
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "login" | "register")}>
          <TabsList className="grid grid-cols-2 w-full rounded-none">
            <TabsTrigger 
              value="login"
              className="data-[state=active]:bg-red-50 data-[state=active]:text-red-600 py-3 rounded-none"
            >
              Anmelden
            </TabsTrigger>
            <TabsTrigger 
              value="register"
              className="data-[state=active]:bg-red-50 data-[state=active]:text-red-600 py-3 rounded-none"
            >
              Registrieren
            </TabsTrigger>
          </TabsList>

          <CardContent className="px-6 pt-6">
            <TabsContent value="login">
              <LoginForm 
                email={email}
                setEmail={setEmail}
                password={password}
                setPassword={setPassword}
              />
            </TabsContent>

            <TabsContent value="register">
              <RegisterForm 
                name={name}
                setName={setName}
                email={email}
                setEmail={setEmail}
                password={password}
                setPassword={setPassword}
                setActiveTab={setActiveTab}
              />
            </TabsContent>
          </CardContent>
        </Tabs>
      </Card>
    </div>
  );
};

export default Login;
