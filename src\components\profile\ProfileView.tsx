
import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/context/auth';
import { toast } from 'sonner';

const ProfileView: React.FC = () => {
  const {
    user,
    updateUser
  } = useAuth();
  const [name, setName] = useState(user?.name || '');
  const [email, setEmail] = useState(user?.email || '');
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  
  if (!user) {
    return <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">Bitte melden Sie sich an, um Ihr Profil zu sehen.</p>
        </CardContent>
      </Card>;
  }
  
  const handleSave = () => {
    if (newPassword && newPassword !== confirmPassword) {
      toast.error('Die Passwörter stimmen nicht überein.');
      return;
    }

    // Update user info
    updateUser({
      ...user,
      name,
      email,
      ...(newPassword ? {
        password: newPassword
      } : {})
    });
    setIsEditing(false);
    setCurrentPassword('');
    setNewPassword('');
    setConfirmPassword('');
    toast.success('Profil erfolgreich aktualisiert.');
  };
  
  return <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Persönliche Informationen</CardTitle>
          <CardDescription>Verwalten Sie Ihre persönlichen Daten und Passwort</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {isEditing ? (
              <>
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input id="name" value={name} onChange={(e) => setName(e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" type="email" value={email} onChange={(e) => setEmail(e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Aktuelles Passwort</Label>
                  <Input id="currentPassword" type="password" value={currentPassword} onChange={(e) => setCurrentPassword(e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="newPassword">Neues Passwort</Label>
                  <Input id="newPassword" type="password" value={newPassword} onChange={(e) => setNewPassword(e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Passwort bestätigen</Label>
                  <Input id="confirmPassword" type="password" value={confirmPassword} onChange={(e) => setConfirmPassword(e.target.value)} />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setIsEditing(false)}>Abbrechen</Button>
                  <Button onClick={handleSave}>Speichern</Button>
                </div>
              </>
            ) : (
              <>
                <div className="flex justify-between items-center">
                  <span className="font-medium">Name:</span>
                  <span>{user.name}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium">Email:</span>
                  <span>{user.email}</span>
                </div>
                <div className="flex justify-end">
                  <Button onClick={() => setIsEditing(true)}>Bearbeiten</Button>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Rolle und Team</CardTitle>
          <CardDescription>Informationen zu Ihrer Rolle und Team</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="font-medium">Rolle:</span>
              <span>{user.role}</span>
            </div>
            {user.teamId && <div className="flex justify-between items-center">
                <span className="font-medium">Team ID:</span>
                <span>{user.teamId}</span>
              </div>}
            {user.mentorId && <div className="flex justify-between items-center">
                <span className="font-medium">Mentor ID:</span>
                <span>{user.mentorId}</span>
              </div>}
          </div>
        </CardContent>
      </Card>
    </div>;
};

export default ProfileView;
