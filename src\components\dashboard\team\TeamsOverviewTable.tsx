
import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { TeamData } from './types';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { useIsMobile } from '@/hooks/use-mobile';

interface TeamsOverviewTableProps {
  teamStats: TeamData[];
}

const TeamsOverviewTable: React.FC<TeamsOverviewTableProps> = ({ teamStats }) => {
  const isMobile = useIsMobile();
  
  return (
    <Card className="w-full border-[1px] shadow-none">
      <CardHeader className="py-1 px-2">
        <CardTitle className="text-sm">Teams-Übersicht</CardTitle>
        <CardDescription className="text-xs">
          {format(new Date(), "'Stand:' d. MMMM yyyy", { locale: de })}
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <div className="w-full">
          <Table>
            <TableHeader>
              <TableRow className="border-b border-gray-100">
                <TableHead className="text-xs font-normal py-1 px-2 whitespace-nowrap">Name</TableHead>
                <TableHead className="text-xs font-normal py-1 px-2 whitespace-nowrap">Mit.</TableHead>
                <TableHead className="text-xs font-normal py-1 px-2 whitespace-nowrap">Bes.</TableHead>
                <TableHead className="text-xs font-normal py-1 px-2 whitespace-nowrap">Verk.</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="text-xs">
              {teamStats.map((team) => (
                <TableRow key={team.id} className="border-b border-gray-100">
                  <TableCell className="text-xs py-1.5 px-2 whitespace-nowrap font-medium">{team.name}</TableCell>
                  <TableCell className="text-xs py-1.5 px-2">{team.memberCount}</TableCell>
                  <TableCell className="text-xs py-1.5 px-2">{team.visitCount}</TableCell>
                  <TableCell className="text-xs py-1.5 px-2">{team.salesCount}</TableCell>
                </TableRow>
              ))}
              {teamStats.length === 0 && (
                <TableRow>
                  <TableCell colSpan={4} className="text-center text-xs py-4 px-2">Keine Teams gefunden</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default TeamsOverviewTable;
