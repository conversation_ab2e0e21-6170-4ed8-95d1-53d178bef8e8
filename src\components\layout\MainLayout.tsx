
import React from 'react';
import { Outlet } from 'react-router-dom';
import { useAuth } from '@/context/auth';
import { Toaster } from '@/components/ui/toaster';
import { useIsMobile } from '@/hooks/use-mobile';
import { MainSidebar } from './MainSidebar';
import { MainHeader } from './MainHeader';

interface MainLayoutProps {
  title?: string;
  children?: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ title, children }) => {
  const { user, logout } = useAuth();
  const isMobile = useIsMobile();

  // Ensure logout is wrapped to return a Promise
  const handleLogout = async () => {
    return await logout();
  };

  return (
    <div className="flex h-screen bg-gray-50 w-full">
      {/* Sidebar */}
      <MainSidebar user={user} logout={handleLogout} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        {/* Navbar */}
        <MainHeader title={title} user={user} isMobile={isMobile} />

        {/* Page Content */}
        <main className="flex-1 overflow-y-auto bg-gray-50 w-full">
          <div className="w-full">
            {children || <Outlet />}
          </div>
        </main>
      </div>
      <Toaster />
    </div>
  );
};

export default MainLayout;
