
import React, { createContext, useContext, useState, useEffect } from "react";
import { useAuth } from "../auth";
import { DataContextType } from "./types";
import { mockAddresses, mockHouses } from "./mockData";
import { Address, House, Visit, Door, ProductEntry, VisitStatus } from "@/types";
import { v4 as uuidv4 } from "uuid";

// Mock data for visits, doors, products
const mockVisits: Visit[] = [];
const mockDoors: Door[] = [];
const mockProducts: ProductEntry[] = [];

// Simple localStorage wrapper
const storage = {
  save: (key: string, data: any) => {
    try {
      localStorage.setItem(key, JSON.stringify(data));
    } catch (e) {
      console.error("Error saving to localStorage", e);
    }
  },
  load: (key: string, defaultValue: any) => {
    try {
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : defaultValue;
    } catch (e) {
      console.error("Error loading from localStorage", e);
      return defaultValue;
    }
  }
};

const DataContext = createContext<DataContextType | undefined>(undefined);

export const DataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [addresses, setAddresses] = useState(() => storage.load("addresses", mockAddresses));
  const [houses, setHouses] = useState(() => storage.load("houses", mockHouses));
  const [visits, setVisits] = useState(() => storage.load("visits", mockVisits));
  const [doors, setDoors] = useState(() => storage.load("doors", mockDoors));
  const [products, setProducts] = useState(() => storage.load("products", mockProducts));
  
  const { user } = useAuth();

  useEffect(() => {
    storage.save("addresses", addresses);
  }, [addresses]);

  useEffect(() => {
    storage.save("houses", houses);
  }, [houses]);

  useEffect(() => {
    storage.save("visits", visits);
  }, [visits]);

  useEffect(() => {
    storage.save("doors", doors);
  }, [doors]);

  useEffect(() => {
    storage.save("products", products);
  }, [products]);

  // CRUD operations
  const addAddress = (addressData: Omit<Address, "id">) => {
    const newAddress: Address = {
      id: uuidv4(),
      ...addressData,
    };
    setAddresses(prev => [...prev, newAddress]);
    return newAddress;
  };

  const getAddressById = (id: string) => {
    return addresses.find(address => address.id === id);
  };

  const addHouse = (houseData: Omit<House, "id" | "createdAt" | "createdBy">) => {
    const newHouse: House = {
      id: uuidv4(),
      ...houseData,
      createdAt: new Date().toISOString(),
      createdBy: user?.id || "unknown",
    };
    setHouses(prev => [...prev, newHouse]);
    return newHouse;
  };

  const getHouseById = (id: string) => {
    return houses.find(house => house.id === id);
  };

  const getHousesByAddress = (addressId: string) => {
    return houses.filter(house => house.addressId === addressId);
  };

  const addVisit = (visitData: Omit<Visit, "id" | "userId">) => {
    const newVisit: Visit = {
      id: uuidv4(),
      ...visitData,
      userId: user?.id || "unknown",
    };
    setVisits(prev => [...prev, newVisit]);
    return newVisit;
  };

  const getVisit = (id: string) => {
    return visits.find(visit => visit.id === id);
  };

  const getVisitsByHouse = (houseId: string) => {
    return visits.filter(visit => visit.houseId === houseId);
  };

  const getTodaysHouses = () => {
    const today = new Date().toISOString().split('T')[0];
    const todayVisits = visits.filter(v => v.timestamp.startsWith(today));
    return todayVisits.map(v => houses.find(h => h.id === v.houseId)).filter(Boolean) as House[];
  };

  const getTodaysVisits = () => {
    const today = new Date().toISOString().split('T')[0];
    return visits.filter(v => v.timestamp.startsWith(today));
  };

  const addDoor = (doorData: Omit<Door, "id">) => {
    const newDoor: Door = {
      id: uuidv4(),
      ...doorData,
    };
    setDoors(prev => [...prev, newDoor]);
    return newDoor;
  };

  const getDoorsByVisit = (visitId: string) => {
    return doors.filter(door => door.visitId === visitId);
  };

  const addProduct = (productData: Omit<ProductEntry, "id" | "userId" | "timestamp">) => {
    const newProduct: ProductEntry = {
      id: uuidv4(),
      ...productData,
      timestamp: new Date().toISOString(),
      userId: user?.id || "unknown",
    };
    setProducts(prev => [...prev, newProduct]);
    return newProduct;
  };

  const getProductsByDoor = (doorId: string) => {
    return products.filter(product => product.doorId === doorId);
  };

  const updateVisitStatus = (visitId: string, status: VisitStatus) => {
    setVisits(prev => prev.map(visit => 
      visit.id === visitId ? { ...visit, status } : visit
    ));
  };

  const updateDoorStatus = (doorId: string, status: VisitStatus) => {
    setDoors(prev => prev.map(door => 
      door.id === doorId ? { ...door, status } : door
    ));
  };

  return (
    <DataContext.Provider
      value={{
        addresses,
        houses,
        visits,
        doors,
        products,
        
        // Address operations
        addAddress,
        getAddressById,
        
        // House operations
        addHouse,
        getHouseById,
        getHousesByAddress,
        
        // Visit operations
        addVisit,
        getVisit,
        getVisitsByHouse,
        getTodaysHouses,
        getTodaysVisits,
        updateVisitStatus,
        
        // Door operations
        addDoor,
        getDoorsByVisit,
        updateDoorStatus,
        
        // Product operations
        addProduct,
        getProductsByDoor
      }}
    >
      {children}
    </DataContext.Provider>
  );
};

export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error("useData must be used within a DataProvider");
  }
  return context;
};
