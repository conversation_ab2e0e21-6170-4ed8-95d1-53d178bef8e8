
import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useData } from '@/context/data';
import { useAuth } from '@/context/auth';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';

interface MentorDashboardProps {
  mentorId: string;
}

const MentorDashboard: React.FC<MentorDashboardProps> = ({ mentorId }) => {
  const { visits, doors, products, houses, addresses, getHouseById } = useData();
  const { users } = useAuth();
  const [activeTab, setActiveTab] = useState<string>("overview");
  
  // Finde alle zugewiesenen Berater
  const assignedBeraters = users.filter(user => {
    return user.mentorId === mentorId;
  });
  
  // Berechne Statistiken pro Berater
  const beraterStats = assignedBeraters.map(berater => {
    const beraterVisits = visits.filter(v => v.userId === berater.id);
    const beraterProducts = products.filter(p => p.userId === berater.id);
    const today = new Date().toISOString().split('T')[0];
    const todayVisits = beraterVisits.filter(v => v.timestamp.startsWith(today));
    
    return {
      id: berater.id,
      name: berater.name,
      totalVisits: beraterVisits.length,
      todayVisits: todayVisits.length,
      salesCount: beraterProducts.length
    };
  });

  // Gesamtstatistiken
  const totalVisits = beraterStats.reduce((sum, berater) => sum + berater.totalVisits, 0);
  const totalSales = beraterStats.reduce((sum, berater) => sum + berater.salesCount, 0);
  const todayTotal = beraterStats.reduce((sum, berater) => sum + berater.todayVisits, 0);
  
  // Get completed addresses (doors with status "Angetroffen → Sale")
  const completedDoors = doors.filter(door => door.status === "Angetroffen → Sale");
  const completedVisits = completedDoors.map(door => {
    const visit = visits.find(v => v.id === door.visitId);
    if (!visit) return null;
    
    // Find berater name
    const berater = users.find(u => u.id === visit.userId);
    
    // Find house and address
    const house = getHouseById(visit.houseId);
    const address = house ? addresses.find(a => a.id === house.addressId) : null;
    
    return {
      doorId: door.id,
      visitId: door.visitId,
      visitDate: visit.timestamp ? new Date(visit.timestamp).toLocaleDateString('de-DE') : 'Unbekannt',
      address: address 
        ? `${address.street} ${house?.houseNumber || ''}, ${address.zipCode} ${address.city}` 
        : 'Unbekannte Adresse',
      beraterName: berater?.name || 'Unbekannt'
    };
  }).filter(Boolean);
  
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Mentor Dashboard</h2>
      <p className="text-muted-foreground">
        Übersicht über {assignedBeraters.length} zugewiesene Berater
      </p>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList>
          <TabsTrigger value="overview">Team Übersicht</TabsTrigger>
          <TabsTrigger value="completed">Abgeschlossene Adressen</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Gesamtbesuche Team</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold">{totalVisits}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Gesamtverkäufe Team</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold">{totalSales}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Heutige Besuche</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold">{todayTotal}</p>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Berater-Übersicht</CardTitle>
              <CardDescription>
                {format(new Date(), "'Stand:' d. MMMM yyyy", { locale: de })}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Besuche gesamt</TableHead>
                    <TableHead>Besuche heute</TableHead>
                    <TableHead>Verkäufe</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {beraterStats.map((berater) => (
                    <TableRow key={berater.id}>
                      <TableCell>{berater.name}</TableCell>
                      <TableCell>{berater.totalVisits}</TableCell>
                      <TableCell>{berater.todayVisits}</TableCell>
                      <TableCell>{berater.salesCount}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="completed" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Abgeschlossene Adressen</CardTitle>
              <CardDescription>
                Adressen, die von Beratern fertig bearbeitet wurden ({completedVisits.length} Einträge)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Datum</TableHead>
                    <TableHead>Adresse</TableHead>
                    <TableHead>Berater</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {completedVisits.map((item: any) => (
                    <TableRow key={item.doorId}>
                      <TableCell>{item.visitDate}</TableCell>
                      <TableCell className="font-medium">{item.address}</TableCell>
                      <TableCell>{item.beraterName}</TableCell>
                      <TableCell>
                        <Badge className="bg-green-500">Abgeschlossen</Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                  {completedVisits.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-4">
                        Keine abgeschlossenen Adressen vorhanden
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MentorDashboard;
