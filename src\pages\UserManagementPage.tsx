
import React, { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/context/auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { User, UserRole } from '@/types';
import { toast } from 'sonner';

const UserManagementPage: React.FC = () => {
  const { users, updateUser, createUser } = useAuth();
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [newUser, setNewUser] = useState({
    name: '',
    email: '',
    password: '',
    role: 'berater' as UserRole,
    teamId: 'team1',
    mentorId: '',
  });

  const handleEditUser = (user: User) => {
    setSelectedUser({ ...user });
  };

  const handleUpdateRole = (role: UserRole) => {
    if (selectedUser) {
      setSelectedUser({ ...selectedUser, role });
    }
  };

  const handleUpdateTeam = (teamId: string) => {
    if (selectedUser) {
      setSelectedUser({ ...selectedUser, teamId });
    }
  };

  const handleUpdateMentor = (mentorId: string) => {
    if (selectedUser) {
      setSelectedUser({ ...selectedUser, mentorId });
    }
  };

  const saveUserChanges = () => {
    if (selectedUser) {
      updateUser(selectedUser);
      toast.success(`Benutzer ${selectedUser.name} wurde aktualisiert`);
      setSelectedUser(null);
    }
  };

  const handleCreateUser = () => {
    if (!newUser.name || !newUser.email || !newUser.password) {
      toast.error('Bitte füllen Sie alle Pflichtfelder aus');
      return;
    }

    createUser(newUser);
    toast.success(`Benutzer ${newUser.name} wurde erstellt`);
    setNewUser({
      name: '',
      email: '',
      password: '',
      role: 'berater',
      teamId: 'team1',
      mentorId: '',
    });
  };

  // Extract unique team IDs from existing users
  const teams = [...new Set(users.filter(u => u.teamId).map(u => u.teamId))];
  
  // Extract mentors from users
  const mentors = users.filter(u => u.role === 'mentor');

  return (
    <MainLayout title="Benutzerverwaltung">
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">Benutzer verwalten</h2>
          <Dialog>
            <DialogTrigger asChild>
              <Button>Neuen Benutzer anlegen</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Neuen Benutzer erstellen</DialogTitle>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={newUser.name}
                    onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                    placeholder="Name des Benutzers"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="email">E-Mail</Label>
                  <Input
                    id="email"
                    type="email"
                    value={newUser.email}
                    onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="password">Passwort</Label>
                  <Input
                    id="password"
                    type="password"
                    value={newUser.password}
                    onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                    placeholder="Passwort"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="role">Rolle</Label>
                  <Select
                    value={newUser.role}
                    onValueChange={(value: UserRole) => setNewUser({ ...newUser, role: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Rolle auswählen" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="berater">Berater</SelectItem>
                      <SelectItem value="mentor">Mentor</SelectItem>
                      <SelectItem value="teamleiter">Teamleiter</SelectItem>
                      <SelectItem value="gebietsmanager">Gebietsmanager</SelectItem>
                      <SelectItem value="admin">Administrator</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="team">Team</Label>
                  <Select
                    value={newUser.teamId || ''}
                    onValueChange={(value) => setNewUser({ ...newUser, teamId: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Team auswählen" />
                    </SelectTrigger>
                    <SelectContent>
                      {teams.map((teamId) => (
                        <SelectItem key={teamId} value={teamId || ''}>
                          {teamId}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                {newUser.role === 'berater' && (
                  <div className="grid gap-2">
                    <Label htmlFor="mentor">Mentor</Label>
                    <Select
                      value={newUser.mentorId || ''}
                      onValueChange={(value) => setNewUser({ ...newUser, mentorId: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Mentor auswählen" />
                      </SelectTrigger>
                      <SelectContent>
                        {mentors.map((mentor) => (
                          <SelectItem key={mentor.id} value={mentor.id}>
                            {mentor.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>
              <Button onClick={handleCreateUser}>Benutzer erstellen</Button>
            </DialogContent>
          </Dialog>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Alle Benutzer</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>E-Mail</TableHead>
                  <TableHead>Rolle</TableHead>
                  <TableHead>Team</TableHead>
                  <TableHead>Mentor</TableHead>
                  <TableHead>Aktionen</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((user) => {
                  const mentor = user.mentorId ? users.find(u => u.id === user.mentorId)?.name : '-';
                  
                  return (
                    <TableRow key={user.id}>
                      <TableCell>{user.name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell className="capitalize">{user.role}</TableCell>
                      <TableCell>{user.teamId || '-'}</TableCell>
                      <TableCell>{mentor}</TableCell>
                      <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm" onClick={() => handleEditUser(user)}>
                              Bearbeiten
                            </Button>
                          </DialogTrigger>
                          {selectedUser && (
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Benutzer bearbeiten: {selectedUser.name}</DialogTitle>
                              </DialogHeader>
                              <div className="grid gap-4 py-4">
                                <div className="grid gap-2">
                                  <Label htmlFor="edit-role">Rolle ändern</Label>
                                  <Select
                                    value={selectedUser.role}
                                    onValueChange={(value: UserRole) => handleUpdateRole(value)}
                                  >
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="berater">Berater</SelectItem>
                                      <SelectItem value="mentor">Mentor</SelectItem>
                                      <SelectItem value="teamleiter">Teamleiter</SelectItem>
                                      <SelectItem value="gebietsmanager">Gebietsmanager</SelectItem>
                                      <SelectItem value="admin">Administrator</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                                
                                <div className="grid gap-2">
                                  <Label htmlFor="edit-team">Team zuweisen</Label>
                                  <Select
                                    value={selectedUser.teamId || ''}
                                    onValueChange={(value) => handleUpdateTeam(value)}
                                  >
                                    <SelectTrigger>
                                      <SelectValue placeholder="Team auswählen" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {teams.map((teamId) => (
                                        <SelectItem key={teamId} value={teamId || ''}>
                                          {teamId}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                                
                                {selectedUser.role === 'berater' && (
                                  <div className="grid gap-2">
                                    <Label htmlFor="edit-mentor">Mentor zuweisen</Label>
                                    <Select
                                      value={selectedUser.mentorId || ''}
                                      onValueChange={(value) => handleUpdateMentor(value)}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder="Mentor auswählen" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {mentors.map((mentor) => (
                                          <SelectItem key={mentor.id} value={mentor.id}>
                                            {mentor.name}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </div>
                                )}
                              </div>
                              <Button onClick={saveUserChanges}>Änderungen speichern</Button>
                            </DialogContent>
                          )}
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default UserManagementPage;
