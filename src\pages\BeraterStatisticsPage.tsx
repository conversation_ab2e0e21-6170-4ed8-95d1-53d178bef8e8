
import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/context/auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useData } from '@/context/data';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

const BeraterStatisticsPage: React.FC = () => {
  const { user, users } = useAuth();
  const { visits, products } = useData();

  if (!user || user.role !== 'mentor') {
    return (
      <MainLayout title="Berater Statistiken">
        <div className="p-4 text-center">
          Sie haben keine Berechtigung, diese Seite anzuzeigen.
        </div>
      </MainLayout>
    );
  }

  // Get all beraters assigned to this mentor
  const assignedBeraters = users.filter(u => u.mentorId === user.id);

  // Prepare data for charts
  const visitData = assignedBeraters.map(berater => {
    const beraterVisits = visits.filter(v => v.userId === berater.id).length;
    return {
      name: berater.name,
      visits: beraterVisits
    };
  });

  const salesData = assignedBeraters.map(berater => {
    const beraterSales = products.filter(p => p.userId === berater.id).length;
    return {
      name: berater.name,
      sales: beraterSales
    };
  });

  return (
    <MainLayout title="Berater Statistiken">
      <div className="space-y-6">
        <h2 className="text-xl font-bold">Statistiken für {assignedBeraters.length} zugewiesene Berater</h2>
        
        <Card>
          <CardHeader>
            <CardTitle>Besuche pro Berater</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[400px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={visitData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="visits" name="Anzahl Besuche" fill="#4f46e5" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Verkäufe pro Berater</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[400px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={salesData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="sales" name="Anzahl Verkäufe" fill="#10b981" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default BeraterStatisticsPage;
