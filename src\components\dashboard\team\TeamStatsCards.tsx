
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { TeamData } from './types';
import { useIsMobile } from '@/hooks/use-mobile';

interface TeamStatsCardsProps {
  teamStats: TeamData[];
}

const TeamStatsCards: React.FC<TeamStatsCardsProps> = ({ teamStats }) => {
  const isMobile = useIsMobile();
  
  return (
    <div className="grid grid-cols-2 gap-2 w-full">
      <Card className="col-span-1 border-[1px] shadow-none">
        <CardHeader className="py-1 px-2">
          <CardTitle className="text-xs font-normal text-muted-foreground">Teams</CardTitle>
        </CardHeader>
        <CardContent className="py-0 px-2 pb-1">
          <p className="text-base font-bold">{teamStats.length}</p>
        </CardContent>
      </Card>
      
      <Card className="col-span-1 border-[1px] shadow-none">
        <CardHeader className="py-1 px-2">
          <CardTitle className="text-xs font-normal text-muted-foreground">Besuche</CardTitle>
        </CardHeader>
        <CardContent className="py-0 px-2 pb-1">
          <p className="text-base font-bold">
            {teamStats.reduce((sum, team) => sum + team.visitCount, 0)}
          </p>
        </CardContent>
      </Card>
      
      <Card className="col-span-1 border-[1px] shadow-none">
        <CardHeader className="py-1 px-2">
          <CardTitle className="text-xs font-normal text-muted-foreground">Verkäufe</CardTitle>
        </CardHeader>
        <CardContent className="py-0 px-2 pb-1">
          <p className="text-base font-bold">
            {teamStats.reduce((sum, team) => sum + team.salesCount, 0)}
          </p>
        </CardContent>
      </Card>
      
      <Card className="col-span-1 border-[1px] shadow-none">
        <CardHeader className="py-1 px-2">
          <CardTitle className="text-xs font-normal text-muted-foreground">Heute</CardTitle>
        </CardHeader>
        <CardContent className="py-0 px-2 pb-1">
          <p className="text-base font-bold">
            {teamStats.reduce((sum, team) => sum + team.todayVisits, 0)}
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default TeamStatsCards;
