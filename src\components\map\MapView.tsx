
import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useData } from '@/context/data';
import { useIsMobile } from '@/hooks/use-mobile';

const MapView: React.FC = () => {
  const { houses, visits } = useData();
  const [mapLoaded, setMapLoaded] = useState(false);
  const isMobile = useIsMobile();

  useEffect(() => {
    // In a real app, we would use a mapping library like Leaflet or Google Maps
    // For this prototype, we'll simulate a map loading
    const timer = setTimeout(() => {
      setMapLoaded(true);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Count completed and pending houses
  const completedHouses = houses.filter(house => {
    const houseVisits = visits.filter(v => v.houseId === house.id);
    return houseVisits.some(v => 
      v.status === 'Angetroffen → Sale' || 
      v.status === 'Angetroffen → Kein Interesse'
    );
  }).length;

  const pendingHouses = houses.length - completedHouses;

  return (
    <div className="w-full h-full">
      <Card className="h-full w-full border-0 rounded-none md:border md:rounded-lg">
        <CardHeader className="pb-2">
          <CardTitle className="text-base">Kartenansicht</CardTitle>
        </CardHeader>
        <CardContent className="p-0 h-[calc(100%-60px)]">
          <div className="relative w-full h-full">
            {!mapLoaded ? (
              <div className="flex items-center justify-center h-full w-full">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
              </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <div className="absolute top-0 left-0 w-full h-full">
                  {/* Real map implementation would go here */}
                  <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                    <p className="text-muted-foreground">
                      Kartenvisualisierung der Besuche
                    </p>
                  </div>
                </div>
                
                <div className="absolute bottom-4 left-4 flex flex-col space-y-2 bg-white bg-opacity-80 p-2 rounded-md">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    <span className="text-xs">Abgeschlossene Besuche ({completedHouses})</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                    <span className="text-xs">Offene Besuche ({pendingHouses})</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MapView;
